package com.wexl.erp.fees.repository;

import com.wexl.erp.fees.model.*;
import com.wexl.retail.model.Student;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface FeeHeadRepository extends JpaRepository<FeeHead, Long> {

  boolean existsByFeeMasterAndStudentAndFeeType(
      FeeMaster feeMaster, Student student, FeeType feeType);

  List<FeeHead> findAllByFeeMasterAndOrgSlug(FeeMaster feeMaster, String orgSlug);

  List<FeeHead> findAllByStudentIdAndOrgSlugAndStatusNotIn(
      Long studentId, String orgSlug, List<FeeStatus> status);

  Optional<FeeHead> findByIdAndOrgSlug(UUID uuid, String orgSlug);

  List<FeeHead> findAllByConcession(Concession concession);

  Long countByFeeTypeIn(List<FeeType> feeTypes);

  Optional<FeeHead> findByIdAndOrgSlugAndStudent(UUID id, String orgSlug, Student student);

  @Query(value = """
          SELECT fh.*,fg.description as feeName
          FROM fee_heads fh
                     JOIN fee_masters fm ON fm.id = fh.fee_master_id
                     JOIN fee_groups fg ON fg.id = fm.fee_group_id
           WHERE fh.org_slug = :orgSlug
             AND fh.student_id IN (:studentIds)
             AND fh.status IN (0, 2)
             AND fg.is_active = TRUE
             AND fh.due_date >= :fromDate
             AND fh.due_date <= :toDate
           ORDER BY fh.student_id, fh.due_date""", nativeQuery = true)
  List<FeeHead> findPastDueFeeDetails(
          @Param("orgSlug") String orgSlug,
          @Param("studentIds") List<Long> studentIds,
          @Param("fromDate") LocalDateTime fromDate,
          @Param("toDate") LocalDateTime toDate);


  @Query(value = """
          SELECT fh.*,fg.description as feeName
          FROM fee_heads fh
                    JOIN fee_masters fm ON fm.id = fh.fee_master_id
                    JOIN fee_groups fg ON fg.id = fm.fee_group_id
          WHERE fh.org_slug = :orgSlug
            AND fh.student_id IN (:studentIds)
            AND fh.status IN (3)
            AND fg.is_active = TRUE
            AND fh.due_date >= :fromDate
            AND fh.due_date <= :toDate
          ORDER BY fh.student_id, fh.due_date""", nativeQuery = true)
  List<FeeHead> findTotalDueFeeDetails(
          @Param("orgSlug") String orgSlug,
          @Param("studentIds") List<Long> studentIds,
          @Param("fromDate") LocalDateTime fromDate,
          @Param("toDate") LocalDateTime toDate);
}
