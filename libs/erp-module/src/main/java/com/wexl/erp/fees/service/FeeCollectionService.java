package com.wexl.erp.fees.service;

import com.wexl.erp.fees.dto.FeeDto;
import com.wexl.erp.fees.model.FeeHead;
import com.wexl.erp.fees.model.FeePayment;
import com.wexl.erp.fees.model.FeePaymentDetail;
import com.wexl.erp.fees.model.FeeStatus;
import com.wexl.erp.fees.repository.FeeGroupFeeTypeRepository;
import com.wexl.erp.fees.repository.FeeHeadRepository;
import com.wexl.erp.fees.repository.FeePaymentDetailsRepository;
import com.wexl.erp.fees.repository.FeePaymentRepository;
import com.wexl.erp.paymentGateway.dto.PaymentGatewayDto;
import com.wexl.erp.paymentGateway.dto.PaymentMethod;
import com.wexl.erp.paymentGateway.dto.PaymentStatus;
import com.wexl.erp.paymentGateway.model.PaymentGatewayDetail;
import com.wexl.erp.paymentGateway.repository.PaymentGatewayDetailRepository;
import com.wexl.erp.paymentGateway.types.PaymentGatewayEngine;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class FeeCollectionService {

  private final FeeHeadRepository feeHeadRepository;
  private final PaymentGatewayEngine paymentGatewayEngine;
  private final PaymentGatewayDetailRepository paymentGatewayDetailRepository;
  private final FeePaymentRepository feePaymentRepository;
  private final FeePaymentDetailsRepository feePaymentDetailsRepository;
  private final FeeHeadService feeHeadService;
  private final FeeGroupFeeTypeRepository feeGroupFeeTypeRepository;

  public PaymentGatewayDto.InitiatePaymentResponse collectFee(
      String feeHeadId, String orgSlug, FeeDto.CollectFeeRequest request) {
    var feeHead = getFeeHeadById(feeHeadId, orgSlug);
    var config = getConfig(orgSlug, request.paymentMethod());
    validateBalanceAmount(feeHead, request);
    var response = paymentGatewayEngine.initiatePayment(orgSlug, request, feeHead, config);
    if (response.referenceId() == null || response.referenceId().isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Payment initiation failed");
    }
    var feePayment = updateFeePayment(feeHead, request, response, config, orgSlug);
    var feeGroupFeeType =
        feeGroupFeeTypeRepository.findByFeeGroupIdAndOrgSlug(
            feeHead.getFeeMaster().getFeeGroup().getId(), feeHead.getOrgSlug());
    var feeHeadFeeType =
        feeGroupFeeType.stream()
            .filter(x -> x.getFeeType().equals(feeHead.getFeeType()))
            .findFirst()
            .orElseThrow(
                () ->
                    new IllegalArgumentException(
                        "Fee type not found for fee head: " + feeHead.getId()));
    var fineAmount = feeHeadService.calculateFineAmount(feeHead, feeHeadFeeType);
    var totalAmount = feeHead.getBalanceAmount();
    if (feeHead.getPaidAmount() == null) {
      totalAmount = feeHead.getBalanceAmount() + fineAmount;
    }
    feeHead.setStatus(getFeeStatus(feeHead, request.amount()));
    feeHead.setPaidAmount(
        feeHead.getPaidAmount() != null
            ? feeHead.getPaidAmount() + request.amount()
            : request.amount());
    feeHead.setBalanceAmount(totalAmount - request.amount());
    feeHeadRepository.save(feeHead);
    return PaymentGatewayDto.InitiatePaymentResponse.builder()
        .paymentId(feePayment.getId().toString())
        .paymentStatus(feePayment.getPaymentStatus())
        .rzPayOrderId(
            request.paymentMethod().equals(PaymentMethod.CASH_PAYMENT)
                ? null
                : response.referenceId())
        .rzPayKey(config.getConfig() == null ? null : config.getConfig().getKeyId())
        .message("Payment initiated successfully")
        .build();
  }

  private PaymentGatewayDetail getConfig(String orgSlug, PaymentMethod paymentMethod) {
    var config =
        paymentGatewayDetailRepository.findByOrgSlugAndPaymentMethod(orgSlug, paymentMethod);
    if (config == null) {
      throw new ApiException(
          InternalErrorCodes.SERVER_ERROR,
          "RazorPay configuration not found for organization: " + orgSlug);
    }
    return config;
  }

  private FeePayment updateFeePayment(
      FeeHead feeHead,
      FeeDto.CollectFeeRequest request,
      PaymentGatewayDto.Response response,
      PaymentGatewayDetail config,
      String orgSlug) {

    FeePayment feePayment = new FeePayment();
    feePayment.setOrgSlug(orgSlug);
    feePayment.setPaymentMethod(request.paymentMethod());
    feePayment.setPaymentGatewayDetail(config);
    feePayment.setReferenceId(response.referenceId());
    feePayment.setStudent(feeHead.getStudent());
    feePayment.setTotalAmountPaid(request.amount());
    feePayment.setPaymentStatus(
        request.paymentMethod().equals(PaymentMethod.CASH_PAYMENT)
            ? PaymentStatus.SUCCESS
            : PaymentStatus.INITIATED);

    feePaymentRepository.save(feePayment);
    feePaymentDetailsRepository.save(
        FeePaymentDetail.builder()
            .feeHead(feeHead)
            .amountPaid(request.amount())
            .feePayment(feePayment)
            .build());

    return feePayment;
  }

  private void validateBalanceAmount(FeeHead feeHead, FeeDto.CollectFeeRequest request) {
    if (request.amount() <= 0) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "Payment Amount must be greater than zero");
    }
    if (feeHead.getBalanceAmount() <= 0) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "No Pending Fees");
    }
  }

  private FeeStatus getFeeStatus(FeeHead feeHead, Double amount) {
    double balanceAmount = feeHead.getBalanceAmount() != null ? feeHead.getBalanceAmount() : 0.0;
    double fineAmount = feeHead.getFineAmount() != null ? feeHead.getFineAmount() : 0.0;
    double remaining = balanceAmount - amount;
    if (remaining <= 0) {
      return FeeStatus.PAID;
    } else if (amount > 0 && remaining > 0 && amount < (balanceAmount + fineAmount)) {
      return FeeStatus.PARTIALLY_PAID;
    } else {
      return FeeStatus.UNPAID;
    }
  }

  private FeeHead getFeeHeadById(String feeHeadId, String orgSlug) {
    return feeHeadRepository
        .findByIdAndOrgSlug(UUID.fromString(feeHeadId), orgSlug)
        .orElseThrow(
            () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "Fee head not found"));
  }

  public PaymentGatewayDto.PaymentResponse verifyPayment(
      PaymentGatewayDto.VerifyPaymentRequest verifyPaymentRequest,
      String orgSlug,
      String paymentId) {
    try {
      var config = getConfig(orgSlug, verifyPaymentRequest.paymentMethod());
      paymentGatewayEngine.verifyPayment(orgSlug, paymentId, verifyPaymentRequest, config);
      var payment = getPaymentById(paymentId, orgSlug);
      validatePaymentByStatus(payment, PaymentStatus.INITIATED);
      return buildPaymentResponse(updatePayment(payment, verifyPaymentRequest));
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }

  private FeePayment getPaymentById(String paymentId, String orgSlug) {
    return feePaymentRepository
        .findByIdAndOrgSlug(UUID.fromString(paymentId), orgSlug)
        .orElseThrow(
            () ->
                new ApiException(
                    InternalErrorCodes.INVALID_REQUEST,
                    "Order not initiated for the given status"));
  }

  private FeePayment updatePayment(
      FeePayment feePayment, PaymentGatewayDto.VerifyPaymentRequest verifyPaymentRequest) {
    feePayment.setPaymentStatus(PaymentStatus.SUCCESS);
    feePayment.setRazorPayTransactionId(verifyPaymentRequest.razorpayOrderId());
    feePayment.setRazorpayPaymentId(verifyPaymentRequest.razorpayPaymentId());
    return feePaymentRepository.save(feePayment);
  }

  private PaymentGatewayDto.PaymentResponse buildPaymentResponse(FeePayment feePayment) {
    return PaymentGatewayDto.PaymentResponse.builder()
        .paymentId(feePayment.getId().toString())
        .status(feePayment.getPaymentStatus())
        .message("Payment verified successfully")
        .build();
  }

  private void validatePaymentByStatus(FeePayment payment, PaymentStatus status) {
    if (payment.getPaymentStatus() != status) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "Payment not initiated for the given status: " + status);
    }
  }
}
