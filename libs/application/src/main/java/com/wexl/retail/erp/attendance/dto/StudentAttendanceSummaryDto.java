package com.wexl.retail.erp.attendance.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Builder;

public record StudentAttendanceSummaryDto() {

  @Builder
  public record Response(
      @JsonProperty("admission_no") String admissionNo,
      @JsonProperty("class_roll_number") String classRollNumber,
      @JsonProperty("student_name") String studentName,
      @JsonProperty("student_id") Long studentId,
      @JsonProperty("present_count") Integer presentCount,
      @JsonProperty("absent_count") Integer absentCount,
      @JsonProperty("late_comer_count") Integer lateComerCount,
      @JsonProperty("half_day_count") Integer halfDayCount,
      @JsonProperty("leave_count") Integer leaveCount,
      @JsonProperty("ptm_count") Integer ptmCount,
      @JsonProperty("nid_count") Integer nidCount,
      @JsonProperty("monthly_working_days") Integer monthlyWorkingDays,
      @JsonProperty("total_working_days") Integer totalWorkingDays,
      @JsonProperty("total_attendance") Integer totalAttendance,
      @JsonProperty("total_attendance_percentage") Double totalAttendancePercentage,
      @JsonProperty("attendance_details") List<AttendanceDetail> attendanceDetails) {}

  @Builder
  public record AttendanceDetail(
      @JsonProperty("date") Long date, @JsonProperty("status") String status) {}
}
