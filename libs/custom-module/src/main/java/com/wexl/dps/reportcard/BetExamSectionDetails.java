package com.wexl.dps.reportcard;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@Entity
@AllArgsConstructor
@RequiredArgsConstructor
@Table(name = "bet_exam_section_details")
public class BetExamSectionDetails extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(name = "section_name")
  private String sectionName;

  @Column(name = "section_value")
  private String sectionValue;

  @Column(name = "section_grade")
  private String sectionGrade;

  @Column(name = "question_count")
  private Long questionCount;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "bet_exam_details_id", nullable = false)
  private BetExamDetails betExamDetails;
}
