package com.wexl.dps.reportcard;

import com.wexl.dps.dto.BetReportDto;
import com.wexl.retail.model.Model;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.hibernate.annotations.Type;

@Entity
@Data
@RequiredArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "bet_exam_details")
public class BetExamDetails extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(name = "exam_id", unique = true)
  private Long examId;

  @Column(name = "org_slug")
  private String orgSlug;

  @Column(name = "exam_date")
  private String examDate;

  @Column(name = "test_level")
  private String testLevel;

  @Column(name = "proficiency_value")
  private String proficiencyValue;

  @Type(JsonType.class)
  @Column(columnDefinition = "jsonb")
  private BetReportDto.PronunciationAssessment speechAnalysis;

  @Column(name = "overall_score")
  private Double overallScore;

  @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
  private List<BetExamSectionDetails> betExamSectionDetails;
}
