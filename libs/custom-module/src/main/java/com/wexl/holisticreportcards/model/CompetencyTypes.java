package com.wexl.holisticreportcards.model;

public enum CompetencyTypes {
  BEGINNER("beginner"),
  PROGRESSING("progressing"),
  PROFICIENT("Proficient"),
  N_A("Not Applicable"),
  EXEMPLARY("Exemplary"),
  SPECIAL_NEEDS("special_needs"),
  NEEDS_ENCOURAGEMENT("Needs Encouragement"),
  ADVANCED("advanced");

  private String value;

  CompetencyTypes(String value) {
    this.value = value;
  }

  public String getValue() {
    return value;
  }
}
